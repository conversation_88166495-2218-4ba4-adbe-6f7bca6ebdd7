'use client';

import { useEffect, useRef } from 'react';
import { useAppKitNetwork, useAppKitAccount  } from '@reown/appkit/react'

interface AppKitButtonProps {
  className?: string;
}

export default function AppKitButton({ className }: AppKitButtonProps) {
  const buttonRef = useRef<HTMLDivElement>(null);
   const { chainId } = useAppKitNetwork()
   const { isConnected } = useAppKitAccount() // AppKit hook to get the address and check if the user is connected

  // Set the chain attribute on the appkit-button element
  useEffect(() => {
    if (buttonRef.current) {
      const chain = chainId?.toString() || '25'; // Default to Cronos chain ID
      buttonRef.current.setAttribute('chain', chain);
    }
  }, [chainId]);

  // Log connection status for debugging
  useEffect(() => {
    console.log('[AppKitButton] isConnected:', isConnected);
  }, [isConnected]);

  return <div ref={buttonRef} className={className}><appkit-button /></div>;
}
