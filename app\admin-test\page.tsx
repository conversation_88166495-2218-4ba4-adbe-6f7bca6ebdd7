'use client';

import { useState, useEffect } from 'react';
import { useAppKitAccount } from '@reown/appkit/react';

interface User {
  address: string;
  role: string;
  status: string;
  name?: string;
}

export default function AdminTestPage() {
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAdmin, setIsAdmin] = useState(false);
  const [debugInfo, setDebugInfo] = useState<any>({});
  const { address, isConnected } = useAppKitAccount();

  useEffect(() => {
    const fetchAndCheck = async () => {
      try {
        setLoading(true);
        
        // Update debug info
        setDebugInfo({
          isConnected,
          address,
          timestamp: new Date().toISOString()
        });

        console.log('AdminTest - isConnected:', isConnected);
        console.log('AdminTest - address:', address);

        if (!isConnected || !address) {
          console.log('AdminTest - Not connected, skipping fetch');
          return;
        }

        // Fetch users
        const response = await fetch('/api/admin/users');
        if (!response.ok) {
          throw new Error('Failed to fetch users');
        }
        
        const data = await response.json();
        console.log('AdminTest - Fetched users:', data);
        setUsers(data);

        // Check if current user is admin
        const currentUser = data.find((user: User) => user.address.toLowerCase() === address.toLowerCase());
        console.log('AdminTest - Current user:', currentUser);
        
        if (currentUser && currentUser.role === 'admin') {
          console.log('AdminTest - User is admin!');
          setIsAdmin(true);
        } else {
          console.log('AdminTest - User is not admin');
          setIsAdmin(false);
        }

      } catch (error) {
        console.error('AdminTest - Error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAndCheck();
  }, [isConnected, address]);

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Admin Test Page</h1>
      
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded mb-4">
        <h2 className="text-lg font-semibold mb-2">Debug Info</h2>
        <p><strong>Is Connected:</strong> {String(isConnected)}</p>
        <p><strong>Address:</strong> {address || 'None'}</p>
        <p><strong>Is Admin:</strong> {String(isAdmin)}</p>
        <p><strong>Loading:</strong> {String(loading)}</p>
        <p><strong>Total Users:</strong> {users.length}</p>
      </div>

      {loading && <p>Loading...</p>}

      {!loading && (
        <div>
          <h2 className="text-lg font-semibold mb-2">All Users</h2>
          <div className="space-y-2">
            {users.map((user) => (
              <div 
                key={user.address} 
                className={`p-2 border rounded ${
                  user.address.toLowerCase() === address?.toLowerCase() 
                    ? 'bg-blue-100 dark:bg-blue-900 border-blue-500' 
                    : 'bg-white dark:bg-gray-700'
                }`}
              >
                <p><strong>Address:</strong> {user.address}</p>
                <p><strong>Role:</strong> {user.role}</p>
                <p><strong>Status:</strong> {user.status}</p>
                {user.name && <p><strong>Name:</strong> {user.name}</p>}
                {user.address.toLowerCase() === address?.toLowerCase() && (
                  <p className="text-blue-600 dark:text-blue-400 font-semibold">← This is you!</p>
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {!isConnected && (
        <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded mt-4">
          <p>Please connect your wallet to test admin access.</p>
        </div>
      )}

      {isConnected && !loading && !isAdmin && (
        <div className="bg-red-100 dark:bg-red-900 p-4 rounded mt-4">
          <p>You are not an admin user.</p>
        </div>
      )}

      {isConnected && !loading && isAdmin && (
        <div className="bg-green-100 dark:bg-green-900 p-4 rounded mt-4">
          <p>✅ You have admin access!</p>
        </div>
      )}
    </div>
  );
}
