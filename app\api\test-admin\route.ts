import { NextRequest } from 'next/server';
import { db } from '@/db/drizzle';
import { web3Profile } from '@/db/schema';
import { eq } from 'drizzle-orm';

export async function GET(request: NextRequest): Promise<Response> {
  try {
    const { searchParams } = new URL(request.url);
    const address = searchParams.get('address');

    console.log('Test admin endpoint - checking address:', address);

    // Get all users
    const allUsers = await db.select().from(web3Profile);
    console.log('Test admin endpoint - all users:', allUsers.length);

    // Check specific user if address provided
    if (address) {
      const specificUser = allUsers.find(user => user.address.toLowerCase() === address.toLowerCase());
      console.log('Test admin endpoint - specific user:', specificUser);

      return Response.json({
        success: true,
        totalUsers: allUsers.length,
        requestedAddress: address,
        foundUser: specificUser,
        allUsers: allUsers.map(u => ({ address: u.address, role: u.role, status: u.status }))
      });
    }

    return Response.json({
      success: true,
      totalUsers: allUsers.length,
      allUsers: allUsers.map(u => ({ address: u.address, role: u.role, status: u.status }))
    });
  } catch (error) {
    console.error('Test admin endpoint error:', error);
    return Response.json(
      { error: 'Failed to test admin endpoint', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
