"use client";
import {
  Navbar,
  NavBody,
  NavItems,
  MobileNav,
  NavbarLogo,
  NavbarButton,
  MobileNavHeader,
  MobileNavToggle,
  MobileNavMenu,
} from "@/components/ui/resizable-navbar";
import React, { useState, useEffect } from "react";
import { usePathname } from "next/navigation";
import Image from "next/image";
import AppKitButton from "./AppKitButton";

export default function SocialNavbar() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Navigation items with simplified labels
  const navItems = [
    {
      name: "Home",
      link: "/",
    },
    {
      name: "Create",
      link: "/createpage",
    },
    {
      name: "Layout",
      link: "/layoutpage",
    },
    {
      name: "View",
      link: "/view",
    },
    {
      name: "Discover",
      link: "/discover",
    },
  ];

  // Function to check if menu and wallet are too close
  const checkMenuWalletProximity = () => {
    if (typeof window === 'undefined') return;

    // Use requestAnimationFrame for performance
    requestAnimationFrame(() => {
      const menuContainer = document.querySelector('.menu-container') as HTMLElement;
      const lastMenuItem = document.querySelector('.social-navbar-items a:last-of-type') as HTMLElement;
      const walletButton = document.querySelector('.wallet-validator-button') as HTMLElement;

      if (!menuContainer || !lastMenuItem || !walletButton) return;

      const lastMenuItemRect = lastMenuItem.getBoundingClientRect();
      const walletButtonRect = walletButton.getBoundingClientRect();

      // Calculate the distance between the last menu item and the wallet button
      const distance = walletButtonRect.left - (lastMenuItemRect.left + lastMenuItemRect.width);

      // If they're too close, adjust both the menu position and wallet button size
      if (distance < 20) {
        // Add compact mode to wallet button
        walletButton.classList.add('wallet-compact');

        // Move the menu to the left
        const currentLeft = parseInt(menuContainer.style.left || '0', 10);
        menuContainer.style.left = `${currentLeft - (20 - distance)}px`;
      } else if (distance > 40 && menuContainer.style.left) {
        // If there's enough space and menu has been moved, gradually move it back
        const currentLeft = parseInt(menuContainer.style.left, 10);
        if (currentLeft < 0) {
          menuContainer.style.left = `${Math.min(0, currentLeft + 5)}px`;
        }

        // Remove compact mode if there's enough space
        if (distance > 60) {
          walletButton.classList.remove('wallet-compact');
        }
      }
    });
  };

  // Check proximity on mount and window resize, and continuously
  useEffect(() => {
    // Initial check
    checkMenuWalletProximity();

    // Set up resize listener
    window.addEventListener('resize', checkMenuWalletProximity);

    // Set up continuous checking for smooth adjustments
    let animationFrameId: number;

    const continuousCheck = () => {
      checkMenuWalletProximity();
      animationFrameId = requestAnimationFrame(continuousCheck);
    };

    animationFrameId = requestAnimationFrame(continuousCheck);

    // Clean up
    return () => {
      window.removeEventListener('resize', checkMenuWalletProximity);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  // Custom styles for the navigation items and wallet button
  const customNavItemsStyle = `
    /* Style the wallet button to match menu buttons */
    .wallet-validator-button {
      position: relative;
      padding: 0;
      border-radius: 0.375rem; /* rounded-md to match menu buttons */
      background-color: transparent;
      color: hsl(var(--foreground));
      font-size: 0.7rem;
      font-weight: 500;
      transition: all 0.2s;
      height: 18px;
      display: flex;
      align-items: center;
      margin: 0;
    }

    .wallet-validator-button:hover {
      background-color: transparent;
      color: hsl(var(--accent-foreground));
    }

    appkit-button {
      height: 18px !important;
      overflow: visible !important;
      display: flex !important;
      align-items: center !important;
      font-size: 0.7rem !important;
      font-weight: 500 !important;
      transform: scale(0.85) !important;
    }

    appkit-button::part(button) {
      height: 18px !important;
      display: flex !important;
      align-items: center !important;
      padding: 0.25rem 0.375rem !important;
      background-color: hsl(var(--background)) !important;
      border: 1px solid hsl(var(--border)) !important;
      border-radius: 0.375rem !important; /* rounded-md to match menu buttons */
    }

    /* Style for disconnected state - match menu buttons */
    appkit-button:not([connected])::part(button) {
      background-color: hsl(var(--background)) !important;
      color: hsl(var(--foreground)) !important;
      border: 1px solid hsl(var(--border)) !important;
    }

    /* Style for connected state - match menu buttons */
    appkit-button[connected]::part(button) {
      background-color: hsl(var(--background)) !important;
      color: hsl(var(--foreground)) !important;
      border: 1px solid hsl(var(--border)) !important;
    }

    /* Style for hover state - match menu buttons */
    .wallet-validator-button:hover appkit-button::part(button) {
      background-color: hsl(var(--accent)) !important;
      color: hsl(var(--accent-foreground)) !important;
    }

    /* Menu container transition */
    .menu-container {
      transition: left 0.1s ease-out;
    }

    /* Wallet compact mode when close to menu */
    .wallet-compact {
      transform: scale(0.8);
    }

    .wallet-compact appkit-button::part(button) {
      padding: 0.25rem 0.25rem !important;
    }

    .wallet-compact appkit-button::part(balance),
    .wallet-compact appkit-button::part(address),
    .wallet-compact appkit-button::part(chain-icon),
    .wallet-compact appkit-button::part(dropdown-icon) {
      display: none !important;
    }

    /* Hide wallet amount on screens between 375px and 767px */
    @media (min-width: 375px) and (max-width: 767px) {
      appkit-button::part(balance) {
        display: none !important;
      }
      appkit-button::part(address) {
        display: none !important;
      }
      appkit-button::part(chain-icon) {
        display: none !important;
      }
      appkit-button::part(dropdown-icon) {
        display: none !important;
      }
    }
    .social-navbar-items {
      display: flex;
      align-items: center;
      justify-content: center;
      height: 100%;
      padding: 0;
      margin: 0 auto;
    }
    .social-navbar-items a {
      padding: 0.25rem 0.375rem;
      margin: 0 0.125rem;
      font-size: 0.75rem;
      font-weight: 500;
      border-radius: 9999px;
      transition: all 0.2s;
      display: inline-block;
      text-align: center;
    }
    .social-navbar-items a:hover {
      background-color: rgba(59, 130, 246, 0.2);
    }
    .social-navbar-items a[data-active="true"] {
      background-color: rgba(59, 130, 246, 0.3);
      color: white;
    }
    @media (min-width: 640px) {
      .social-navbar-items a {
        padding: 0.375rem 0.75rem;
        margin: 0 0.375rem;
      }
    }
  `;

  // Add additional CSS for sticky behavior
  const stickyNavbarStyle = `
    /* Override the default navbar styles to make it sticky at the top */
    .navbar-override {
      position: fixed !important;
      top: 0 !important;
      left: 0 !important;
      right: 0 !important;
      z-index: 9999 !important;
    }
  `;

  return (
      <div>
        {/* Add custom styles */}
        <style dangerouslySetInnerHTML={{ __html: customNavItemsStyle + stickyNavbarStyle }} />
        <Navbar className="transition-all duration-200 navbar-override">
          {/* Only use NavBody for all screen sizes */}
          <NavBody className="!flex !w-full !min-w-0 !max-w-full pl-2 pr-0 py-2 relative transition-all duration-200 bg-black/80 backdrop-blur-lg border-b border-neutral-800/50 flex-col">
            {/* Row with logo, navigation, and wallet button */}
            <div className="flex items-center justify-between w-full">
              <div className="flex items-center gap-1 flex-shrink-0">
                <div className="flex-shrink-0 w-6 h-6 flex items-center justify-center">
                  <Image
                    src={`/w3t.gif?v=${Date.now()}`}
                    alt="Web3 Socials"
                    width={24}
                    height={24}
                    className="rounded-full"
                    priority
                  />
                </div>
                <span className="hidden sm:inline-block text-sm md:text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
                  Web3 Socials
                </span>
              </div>

              {/* Navigation items centered */}
              <div className="absolute inset-x-0 inset-y-0 flex items-center justify-center menu-container" style={{ position: 'relative', left: '0px' }}>
                <div className="social-navbar-items">
                  {navItems.map((item, idx) => (
                    <a
                      key={`nav-link-${idx}`}
                      href={item.link}
                      data-active={pathname === item.link}
                      className="relative px-4 py-2 text-neutral-300 hover:text-white"
                    >
                      <span className="relative z-20">{item.name}</span>
                    </a>
                  ))}
                </div>
              </div>

              {/* Wallet validator button - positioned at the rightmost part */}
              <div className="flex items-center flex-shrink-0 -mr-2">
                {/* Wallet button styled like menu buttons */}
                <div className="social-navbar-items">
                  <div className="wallet-validator-button">
                    <AppKitButton className="wallet-button" />
                  </div>
                </div>
              </div>
            </div>
          </NavBody>
        </Navbar>
      </div>
  );
}
