'use client';

import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { useAppKit } from '@reown/appkit/react';
import { cn } from '@/lib/utils';

interface CustomConnectButtonProps {
  className?: string;
}

export default function CustomConnectButton({ className }: CustomConnectButtonProps) {
  const { open } = useAppKit();
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  const handleClick = () => {
    open();
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getChainName = (id: string | number) => {
    const chainMap: { [key: string]: string } = {
      '1': 'ETH',
      '25': 'CRO',
      '137': 'MATIC',
      '56': 'BNB',
      '42161': 'ARB',
      '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp': 'SOL',
    };
    return chainMap[id.toString()] || 'Unknown';
  };

  return (
    <button
      onClick={handleClick}
      className={cn(
        // Base styles matching navigation menu buttons
        "inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium",
        // Hover and focus states matching navigation menu buttons
        "hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground",
        // Additional states
        "disabled:pointer-events-none disabled:opacity-50",
        "outline-none transition-all focus-visible:ring-ring/50 focus-visible:ring-[3px] focus-visible:outline-1",
        className
      )}
    >
      {isConnected && address ? (
        <span className="flex items-center gap-2">
          {chainId && (
            <span className="text-xs opacity-75">
              {getChainName(chainId)}
            </span>
          )}
          <span>{formatAddress(address)}</span>
        </span>
      ) : (
        'Connect Wallet'
      )}
    </button>
  );
}
