'use client';

import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';
import { useAppKit } from '@reown/appkit/react';
import { cn } from '@/lib/utils';

interface CustomConnectButtonProps {
  className?: string;
}

export default function CustomConnectButton({ className }: CustomConnectButtonProps) {
  const { open } = useAppKit();
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  const handleClick = () => {
    console.log('Connect button clicked!'); // Debug log
    open();
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  const getChainName = (id: string | number) => {
    const chainMap: { [key: string]: string } = {
      '1': 'ETH',
      '********': 'SEP', // Sepolia testnet
      '25': 'CRO',
      '388': 'zkCRO', // Cronos zkEVM
      '137': 'MATIC',
      '56': 'BNB',
      '42161': 'ARB',
      '5eykt4UsFv8P8NJdTREpY1vzqKqZKvdp': 'SOL',
    };
    return chainMap[id.toString()] || 'Unknown';
  };

  return (
    <button
      onClick={handleClick}
      className={cn(
        // Base styles matching the exact menu button styling
        "relative inline-block text-center text-neutral-300 hover:text-white",
        "transition-all duration-200 cursor-pointer pointer-events-auto",
        // Mobile styles (matches .social-navbar-items a)
        "px-1 py-1 mx-0.5 text-xs font-medium rounded-full",
        // Desktop styles (matches @media (min-width: 640px))
        "sm:px-3 sm:py-1.5 sm:mx-1.5",
        // Ensure it's clickable
        "z-10 select-none",
        className
      )}
      style={{
        // Custom hover background to match menu buttons exactly
        backgroundColor: 'transparent',
      }}
      onMouseEnter={(e) => {
        e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.2)';
      }}
      onMouseLeave={(e) => {
        e.currentTarget.style.backgroundColor = 'transparent';
      }}
      onMouseDown={(e) => {
        e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.3)';
      }}
      onMouseUp={(e) => {
        e.currentTarget.style.backgroundColor = 'rgba(59, 130, 246, 0.2)';
      }}
    >
      {isConnected && address ? (
        <span className="flex items-center gap-2">
          {chainId && (
            <span className="text-xs opacity-75">
              {getChainName(chainId)}
            </span>
          )}
          <span>{formatAddress(address)}</span>
        </span>
      ) : (
        'Connect Wallet'
      )}
    </button>
  );
}
