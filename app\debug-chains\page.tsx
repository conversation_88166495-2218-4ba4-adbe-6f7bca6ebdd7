'use client';

import { useAppKitAccount, useAppKitNetwork } from '@reown/appkit/react';

export default function DebugChainsPage() {
  const { address, isConnected } = useAppKitAccount();
  const { chainId } = useAppKitNetwork();

  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold mb-4">Chain Debug Page</h1>
      
      <div className="bg-gray-100 dark:bg-gray-800 p-4 rounded mb-4">
        <h2 className="text-lg font-semibold mb-2">Current Connection Info</h2>
        <p><strong>Is Connected:</strong> {String(isConnected)}</p>
        <p><strong>Address:</strong> {address || 'None'}</p>
        <p><strong>Chain ID:</strong> {chainId ? String(chainId) : 'None'}</p>
        <p><strong>Chain ID Type:</strong> {chainId ? typeof chainId : 'undefined'}</p>
      </div>

      <div className="bg-blue-100 dark:bg-blue-900 p-4 rounded mb-4">
        <h2 className="text-lg font-semibold mb-2">Instructions</h2>
        <p>Connect to different networks and see what chain IDs are returned:</p>
        <ul className="list-disc list-inside mt-2">
          <li>Cronos should show: 25</li>
          <li>Ethereum should show: 1</li>
          <li>Arbitrum should show: 42161</li>
          <li>Solana should show: ??? (this is what we need to find out)</li>
        </ul>
      </div>

      <div className="bg-yellow-100 dark:bg-yellow-900 p-4 rounded">
        <h2 className="text-lg font-semibold mb-2">Database Issue</h2>
        <p>Current database shows Solana profile with chain: "sol1"</p>
        <p>System settings configured with: "solana"</p>
        <p>We need to find the correct chain ID that AppKit returns for Solana.</p>
      </div>
    </div>
  );
}
